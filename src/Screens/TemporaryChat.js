import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import {colors} from '../Custom/Colors';
import ResponsiveText from '../Custom/RnText';
import CustomHeader from '../Components/CustomHeader';
import AsyncStorage from '@react-native-async-storage/async-storage';
import socket from '../Sockets/socketService';
import Icon from '../Custom/Icon';
import {globalpath} from '../Custom/globalpath';
import {hp, wp} from '../Custom/Responsiveness';
import {useFocusEffect} from '@react-navigation/native';
import LanguageModal from '../Components/LanguageModal';
const getLabel = code => {
  const item = {
    al: 'Albanian',
    bn: 'Bengali',
    ea: 'Egyption',
    en: 'English',
    es: 'Spanish',
    fr: 'French',
    hi: 'Hindi',
    it: 'Italian',
    ma: 'Moroccan',
    sn: 'Shona',
    ta: 'Arabic',
    ur: 'Urdu',
    yo: 'Yoruba',
    zh: 'Chinese',
  };
  return item[code] || code;
};
const TemporaryChat = ({navigation}) => {
  const [user, setUser] = useState(null);
  const [userDetails, setUserDetails] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [language, setLanguage] = useState('it');

  const JoinChatRoom = (chat, idtype) => {
    const conversationId = `${idtype}_${chat.phoneNumber}`;
    socket.emit('joinChat', {chat, userDetails, conversationId, idtype});
  };


  useFocusEffect(
    useCallback(() => {
      const fetchUserData = async () => {
        try {
          const userData = await AsyncStorage.getItem('user');
          if (userData) {
            const parsedUser = JSON.parse(userData);
            console.log('parsed user', parsedUser);
            setUser(parsedUser);
            setUserDetails({
              active: null,
              avatar: null,
              email: null,
              id: parsedUser.user.id,
              name: parsedUser.user.firstName,
              role: 'client',
              phoneNumber: parsedUser.user.phoneNumber,
            });
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        }
      };

      fetchUserData();
    }, []),
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <CustomHeader title="My Profile" />

      <ScrollView contentContainerStyle={styles.container}>
        {/* Header with Logo and Avatar */}
        <View style={styles.header}>
          <Image
            source={globalpath.logo}
            style={styles.logo}
            resizeMode="contain"
          />

          {user && (
            <View style={styles.avatarContainer}>
              <View style={styles.avatar}>
                <ResponsiveText color={colors.white} size={5} weight={'600'}>
                  {user?.user?.firstName?.charAt(0)?.toUpperCase() || ''}
                </ResponsiveText>
              </View>
            </View>
          )}
        </View>

        {/* Profile Card */}
        {user && (
          <View style={styles.profileCard}>
            <View style={styles.profileHeader}>
              <ResponsiveText size={5} weight="bold" style={styles.nameText}>
                {user?.user?.firstName} {user?.user?.lastName}
              </ResponsiveText>
              <View style={{flexDirection:"row",alignItems:"center"}}>
         
                    {/* <TouchableOpacity
                  onPress={() => setShowModal(true)}
                  style={{
                    backgroundColor: colors.white,
                    borderWidth: 1,
                    borderColor: colors.greyborder,
                    paddingVertical: wp(2),
                    paddingHorizontal: wp(3),
                    borderRadius: wp(1.5),
                    // width: wp(2),
                  }}>
                  <ResponsiveText>{getLabel(language)}</ResponsiveText>
                </TouchableOpacity> */}
                <TouchableOpacity
                style={styles.editButton}
                onPress={() => navigation.navigate('Profile', {user: user})}>
                <Icon
                  source={globalpath.edit}
                  size={wp(5)}
                  tintColor={colors.Light_theme_purple}
                />
              </TouchableOpacity>
                </View>
            </View>

            <View style={styles.detailsSection}>
              <View style={styles.detailRow}>
                <View style={styles.iconContainer}>
                  <Icon
                    source={globalpath.phonecall}
                    size={wp(5)}
                    tintColor={colors.white}
                  />
                </View>
                <ResponsiveText size={4} style={styles.detailText}>
                  {user?.user?.phoneNumber}
                </ResponsiveText>
              </View>

              <View style={[styles.detailRow,{justifyContent:"space-between"}]}>
                <View style={{alignItems:"center",flexDirection:"row"}}>
                <View style={styles.iconContainer}>
                  <Icon
                    source={globalpath.language}
                    size={wp(5)}
                    tintColor={colors.white}
                  />
                </View>
                <ResponsiveText size={4} style={styles.detailText}>
                 Language
                </ResponsiveText>
                </View>
                <View>
                     <TouchableOpacity
                  onPress={() => setShowModal(true)}
                  style={{
                    backgroundColor: colors.white,
                    borderWidth: 1,
                    borderColor: colors.greyborder,
                    paddingVertical: wp(3),
                    paddingHorizontal: wp(4),
                    borderRadius: wp(2),
                    width: wp(25),
                  }}>
                  <ResponsiveText>{getLabel(language)}</ResponsiveText>
                </TouchableOpacity>
                </View>
              </View>

              <View style={styles.divider} />
              <View>
                {/* <TouchableOpacity
                  onPress={() => setShowModal(true)}
                  style={{
                    backgroundColor: colors.white,
                    borderWidth: 1,
                    borderColor: colors.greyborder,
                    paddingVertical: wp(3),
                    paddingHorizontal: wp(4),
                    borderRadius: wp(2),
                    width: wp(25),
                  }}>
                  <ResponsiveText>{getLabel(language)}</ResponsiveText>
                </TouchableOpacity> */}

                <LanguageModal
                  visible={showModal}
                  onClose={() => setShowModal(false)}
                  onSelect={val => setLanguage(val)}
                  selectedValue={language}
                />
              </View>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Floating Chat Button */}
      <TouchableOpacity
        style={styles.chatButton}
        onPress={() => {
          navigation.navigate('ChatScreen', {
            item: user.user,
            isFromCustomer: false,
            group: 'client',
            lang: language,
            tempChat: true,
          });
          JoinChatRoom(user.user, 'client');
        }}>
        <Icon source={globalpath.convo} size={wp(7)} tintColor={colors.white} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F5F7FB',
  },
  container: {
    paddingBottom: hp(10),
    alignItems: 'center',
  },
  header: {
    width: '100%',
    alignItems: 'center',
    paddingTop: hp(3),
    paddingBottom: hp(4),
    backgroundColor: colors.white,
    borderBottomLeftRadius: wp(8),
    borderBottomRightRadius: wp(8),
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.05,
    shadowRadius: 12,
    // marginBottom: hp(2),
  },
  logo: {
    width: wp(50),
    height: hp(15),
    marginBottom: hp(2),
  },
  avatarContainer: {
    position: 'absolute',
    bottom: -hp(5),
  },
  avatar: {
    width: wp(22),
    height: wp(22),
    borderRadius: wp(11),
    backgroundColor: colors.Light_theme_purple,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: colors.white,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  profileCard: {
    width: wp(90),
    backgroundColor: colors.white,
    borderRadius: wp(5),
    padding: wp(6),
    marginTop: hp(8),
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: hp(1),
  },
  nameText: {
    color: colors.greyBlack,
    fontSize: wp(5),
  },
  editButton: {
    padding: wp(2),
  },
  detailsSection: {
    // marginTop: hp(1),
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(1.5),
  },
  iconContainer: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    backgroundColor: colors.Light_theme_purple,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(4),
  },
  detailText: {
    color: colors.greyBlack,
    flex: 1,
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginVertical: hp(0.5),
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: wp(90),
    marginTop: hp(4),
  },
  statCard: {
    width: '48%',
    backgroundColor: colors.white,
    borderRadius: wp(4),
    padding: wp(5),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 2,
  },
  statNumber: {
    color: colors.Light_theme_purple,
    marginBottom: hp(1),
  },
  statLabel: {
    color: '#888',
  },
  chatButton: {
    position: 'absolute',
    bottom: hp(4),
    right: wp(6),
    width: wp(16),
    height: wp(16),
    borderRadius: wp(8),
    backgroundColor: colors.Light_theme_purple,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.Light_theme_purple,
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});

export default TemporaryChat;
